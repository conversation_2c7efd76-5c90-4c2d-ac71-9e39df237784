import { useState, useEffect, useRef } from 'react'
import './App.css'
import Background3D from './components/Background3D'

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [openFAQ, setOpenFAQ] = useState(null)
  const [heroOpacity, setHeroOpacity] = useState(1)
  const [statementOpacity, setStatementOpacity] = useState(0)
  const heroRef = useRef(null)
  const statementRef = useRef(null)

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index)
  }

  // Team logos data
  const teamLogos = [
    { src: "/team_background_images/National University of Singapore.png", alt: "National University of Singapore", color: "blue" },
    { src: "/team_background_images/National Taiwan University.png", alt: "National Taiwan University", color: "green" },
    { src: "/team_background_images/IIIT Delhi.png", alt: "IIIT Delhi", color: "purple" },
    { src: "/team_background_images/Indian Institute of Science.png", alt: "Indian Institute of Science", color: "pink" },
    { src: "/team_background_images/Samsung Research.png", alt: "Samsung Research", color: "yellow", scale: true },
    { src: "/team_background_images/Hyperverge.png", alt: "Hyperverge", color: "indigo", scale: true },
    { src: "/team_background_images/University of South Carolina.png", alt: "University of South Carolina", color: "teal" }
  ]

  // Smooth continuous carousel - no JavaScript needed, pure CSS animation

  // Scroll-based fade transitions (faster)
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY
      const windowHeight = window.innerHeight

      // Hero section fade out as we scroll down (faster fade)
      const heroFadeStart = windowHeight * 0.2
      const heroFadeEnd = windowHeight * 0.6
      let heroOpacityValue = 1

      if (scrollY > heroFadeStart) {
        const fadeProgress = (scrollY - heroFadeStart) / (heroFadeEnd - heroFadeStart)
        heroOpacityValue = Math.max(0, 1 - fadeProgress)
      }

      // Statement section fade in as we approach it (faster fade)
      const statementFadeStart = windowHeight * 0.4
      const statementFadeEnd = windowHeight * 0.8
      const statementFadeOutStart = windowHeight * 1.4
      const statementFadeOutEnd = windowHeight * 1.8
      let statementOpacityValue = 0

      if (scrollY > statementFadeStart && scrollY < statementFadeOutStart) {
        if (scrollY < statementFadeEnd) {
          const fadeProgress = (scrollY - statementFadeStart) / (statementFadeEnd - statementFadeStart)
          statementOpacityValue = Math.min(1, fadeProgress)
        } else {
          statementOpacityValue = 1
        }
      } else if (scrollY >= statementFadeOutStart) {
        const fadeProgress = (scrollY - statementFadeOutStart) / (statementFadeOutEnd - statementFadeOutStart)
        statementOpacityValue = Math.max(0, 1 - fadeProgress)
      }

      setHeroOpacity(heroOpacityValue)
      setStatementOpacity(statementOpacityValue)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <div className="min-h-screen w-full relative">
      {/* Three.js Background */}
      <Background3D />

      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm sticky top-0 z-50 w-full relative">
        <nav className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-extrabold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent hover:scale-105 transition-transform duration-300 cursor-pointer tracking-tight">
                Qreate AI
              </h1>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              <a href="#about" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                About Us
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#portfolio" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                Portfolio
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#services" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                Services
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#team" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                Team
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#faqs" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                FAQs
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="https://calendly.com/qareailabs" target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                Contact Us
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-300 hover:text-white transition-colors duration-300"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden animate-fade-in">
              <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-800/50 backdrop-blur-sm rounded-lg">
                <a href="#about" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">About Us</a>
                <a href="#portfolio" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Portfolio</a>
                <a href="#services" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Services</a>
                <a href="#team" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Team</a>
                <a href="#faqs" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">FAQs</a>
                <a href="https://calendly.com/qareailabs" target="_blank" rel="noopener noreferrer" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Contact Us</a>
              </div>
            </div>
          )}
        </nav>
      </header>

      {/* Hero Section - First Page */}
      <section
        ref={heroRef}
        className="relative min-h-screen flex items-start justify-center pt-32 md:pt-40 lg:pt-48 overflow-hidden transition-opacity duration-500 ease-in-out"
        style={{ opacity: heroOpacity }}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/30 backdrop-blur-[1px]"></div>

        <div className="relative w-full px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-extrabold text-white mb-8 animate-fade-in-up tracking-tight leading-none">
            We help you build<br />
            <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent animate-gradient-x">
              really cool stuff
            </span>
          </h1>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto animate-fade-in-up delay-300 font-medium leading-relaxed">
            Qreate AI is an AI-native product studio building fullstack AI/LLM apps
          </p>
          <a href="https://calendly.com/qareailabs" target="_blank" rel="noopener noreferrer" className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-10 rounded-xl text-lg md:text-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25 animate-fade-in-up delay-500 rocket-boost tracking-tight inline-block">
            <span className="flex items-center text-white">
              Book a Call
              <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </span>
          </a>
        </div>
      </section>

      {/* Company Statement Section - Second Page */}
      <section
        ref={statementRef}
        className="relative min-h-screen flex items-center justify-center overflow-hidden transition-opacity duration-500 ease-in-out"
        style={{ opacity: statementOpacity }}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/20 backdrop-blur-[1px]"></div>

        <div className="relative w-full px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white leading-relaxed max-w-6xl mx-auto tracking-tight">
            <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent font-extrabold">
              Here at Qreate AI
            </span>
            {" "}we engineer AI native solutions. We are committed to creating innovative and inspiring world-class products. Come transform your ideas into powerful realities
          </h2>
        </div>
      </section>

      {/* Project Portfolio Section */}
      <section id="portfolio" className="py-24 relative">
        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"></div>
        <div className="relative w-full px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-6 tracking-tight">Project Portfolio</h2>
            <p className="text-xl md:text-2xl text-gray-300 mb-4 font-semibold">Our AI Solutions in Action</p>
            <p className="text-gray-400 text-base md:text-lg font-medium max-w-3xl mx-auto leading-relaxed">Showcasing cutting-edge AI applications across computer vision, environmental forecasting, supply chain optimization, and intelligent systems</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 mb-24">
            {/* Project 1: Vehicle Tracking and Re-Identification */}
            <div className="group bg-gradient-to-br from-blue-900/30 to-blue-800/30 backdrop-blur-sm rounded-2xl border border-blue-500/30 overflow-hidden hover:border-blue-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20">
              <div className="relative aspect-video bg-gray-900/50 overflow-hidden video-container">
                <video
                  className="w-full h-full object-cover portfolio-video"
                  autoPlay
                  loop
                  muted
                  playsInline
                  style={{
                    mixBlendMode: 'multiply',
                    background: 'linear-gradient(135deg, #1e3a8a, #1e40af)'
                  }}
                >
                  <source src="/Project_video_files/1.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 bg-gradient-to-t from-blue-900/60 via-transparent to-transparent"></div>
                <div className="absolute top-4 right-4 z-10">
                  <div className="bg-blue-500/80 backdrop-blur-sm rounded-full px-3 py-1 animate-pulse">
                    <span className="text-white text-sm font-medium">Computer Vision</span>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl md:text-2xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors duration-300 tracking-tight">
                  Vehicle Tracking & Re-Identification
                </h3>
                <p className="text-gray-300 text-sm md:text-base mb-4 leading-relaxed font-medium">
                  Advanced AI system for tracking and re-identifying vehicles across multiple junction points using state-of-the-art computer vision algorithms.
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 0 }}>Object Tracking</span>
                  <span className="bg-purple-500/20 text-purple-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 1 }}>Re-ID</span>
                  <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 2 }}>Junction Analysis</span>
                </div>
                <div className="flex gap-3">
                  <a
                    href="https://docs.google.com/presentation/d/1BIQGH3oxoDyldU-Rh_-rGFmZJGT9XjVRAB3KPWh63IA/edit?usp=sharing"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 hover:text-blue-200 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Presentation
                  </a>
                  <a
                    href="https://github.com/harshp77/Phase-1-The-Bengaluru-Mobility-Challenge-2024"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 bg-gray-600/20 hover:bg-gray-600/30 text-gray-300 hover:text-gray-200 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                    </svg>
                    GitHub
                  </a>
                </div>
              </div>
            </div>

            {/* Project 2: Vehicle Pattern Recognition */}
            <div className="group bg-gradient-to-br from-purple-900/30 to-purple-800/30 backdrop-blur-sm rounded-2xl border border-purple-500/30 overflow-hidden hover:border-purple-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/20">
              <div className="relative aspect-video bg-gray-900/50 overflow-hidden video-container">
                <video
                  className="w-full h-full object-cover portfolio-video"
                  autoPlay
                  loop
                  muted
                  playsInline
                  style={{
                    mixBlendMode: 'multiply',
                    background: 'linear-gradient(135deg, #7c3aed, #8b5cf6)'
                  }}
                >
                  <source src="/Project_video_files/2.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 bg-gradient-to-t from-purple-900/60 via-transparent to-transparent"></div>
                <div className="absolute top-4 right-4 z-10">
                  <div className="bg-purple-500/80 backdrop-blur-sm rounded-full px-3 py-1 animate-pulse">
                    <span className="text-white text-sm font-medium">Pattern Recognition</span>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl md:text-2xl font-bold text-white mb-3 group-hover:text-purple-400 transition-colors duration-300 tracking-tight">
                  Vehicle Pattern Recognition
                </h3>
                <p className="text-gray-300 text-sm md:text-base mb-4 leading-relaxed font-medium">
                  Intelligent pattern recognition system analyzing vehicle behavior and traffic patterns at junction intersections for smart traffic management.
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="bg-purple-500/20 text-purple-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 0 }}>Pattern Analysis</span>
                  <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 1 }}>Traffic AI</span>
                  <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 2 }}>Smart Systems</span>
                </div>
                <div className="flex gap-3">
                  <a
                    href="https://docs.google.com/presentation/d/1NW70dMrwo3I1b9Mvd3uoLGFgT67USRUboYVCpbX9MJY/edit?usp=sharing"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 bg-purple-600/20 hover:bg-purple-600/30 text-purple-300 hover:text-purple-200 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Presentation
                  </a>
                  <a
                    href="https://github.com/harshp77/Phase-2-The-Bengaluru-Mobility-Challenge-2024"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 bg-gray-600/20 hover:bg-gray-600/30 text-gray-300 hover:text-gray-200 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                    </svg>
                    GitHub
                  </a>
                </div>
              </div>
            </div>

            {/* Project 3: DoQ AI Assistant */}
            <div className="group bg-gradient-to-br from-green-900/30 to-green-800/30 backdrop-blur-sm rounded-2xl border border-green-500/30 overflow-hidden hover:border-green-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-green-500/20">
              <div className="relative aspect-video bg-gray-900/50 overflow-hidden video-container">
                <video
                  className="w-full h-full object-cover portfolio-video"
                  autoPlay
                  loop
                  muted
                  playsInline
                  style={{
                    mixBlendMode: 'multiply',
                    background: 'linear-gradient(135deg, #059669, #10b981)'
                  }}
                >
                  <source src="/Project_video_files/3.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 bg-gradient-to-t from-green-900/60 via-transparent to-transparent"></div>
                <div className="absolute top-4 right-4 z-10">
                  <div className="bg-green-500/80 backdrop-blur-sm rounded-full px-3 py-1 animate-pulse">
                    <span className="text-white text-sm font-medium">AI Assistant</span>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl md:text-2xl font-bold text-white mb-3 group-hover:text-green-400 transition-colors duration-300 tracking-tight">
                  DoQ: AI-Powered Intake Assistant
                </h3>
                <p className="text-gray-300 text-sm md:text-base mb-4 leading-relaxed font-medium">
                  Intelligent intake assistant powered by advanced AI to streamline data collection and user interaction processes with natural language understanding.
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 0 }}>NLP</span>
                  <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 1 }}>AI Assistant</span>
                  <span className="bg-purple-500/20 text-purple-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 2 }}>Automation</span>
                </div>
                <div className="flex gap-3">
                  <a
                    href="https://qareai.in"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 bg-green-600/20 hover:bg-green-600/30 text-green-300 hover:text-green-200 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
                    </svg>
                    Live Product
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Second Row of Projects */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-24">
            {/* Empty space for centering on large screens */}
            <div className="hidden lg:block"></div>
            {/* Project 4: AQI and Heatwave Forecasting */}
            <div className="group bg-gradient-to-br from-orange-900/30 to-red-800/30 backdrop-blur-sm rounded-2xl border border-orange-500/30 overflow-hidden hover:border-orange-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-orange-500/20">
              <div className="relative aspect-video bg-gray-900/50 overflow-hidden video-container">
                <video
                  className="w-full h-full object-cover portfolio-video"
                  autoPlay
                  loop
                  muted
                  playsInline
                >
                  <source src="/Project_video_files/4.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute inset-0 bg-orange-500/10 mix-blend-overlay"></div>
              </div>
              <div className="p-6">
                <h3 className="text-xl md:text-2xl font-bold text-white mb-3 group-hover:text-orange-400 transition-colors duration-300 tracking-tight">
                  AQI & Heatwave Forecasting
                </h3>
                <p className="text-gray-300 text-sm md:text-base mb-4 leading-relaxed font-medium">
                  Advanced environmental forecasting system for tier 2 and 3 cities using Federated Learning, Transfer Learning, and Convolution Multiplication of non-image data for accurate AQI and heatwave predictions.
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="bg-orange-500/20 text-orange-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 0 }}>Federated Learning</span>
                  <span className="bg-red-500/20 text-red-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 1 }}>Transfer Learning</span>
                  <span className="bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 2 }}>Environmental AI</span>
                </div>
                <div className="flex gap-3">
                  <a
                    href="https://aqiheatwave.netlify.app/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 bg-orange-600/20 hover:bg-orange-600/30 text-orange-300 hover:text-orange-200 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
                    </svg>
                    Live Demo
                  </a>
                </div>
              </div>
            </div>

            {/* Project 5: Supply Chain Optimization (Confidential) */}
            <div className="group bg-gradient-to-br from-gray-900/30 to-slate-800/30 backdrop-blur-sm rounded-2xl border border-gray-500/30 overflow-hidden hover:border-gray-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-gray-500/20">
              <div className="relative aspect-video bg-gray-900/50 overflow-hidden flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-gray-600 to-gray-800 rounded-full flex items-center justify-center">
                    <svg className="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <p className="text-gray-400 text-sm font-medium">Confidential Project</p>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute inset-0 bg-gray-500/10 mix-blend-overlay"></div>
              </div>
              <div className="p-6">
                <h3 className="text-xl md:text-2xl font-bold text-white mb-3 group-hover:text-gray-400 transition-colors duration-300 tracking-tight">
                  Supply Chain Optimization
                </h3>
                <p className="text-gray-300 text-sm md:text-base mb-4 leading-relaxed font-medium">
                  Advanced supply chain redistribution optimization using Deep Deterministic Policy Gradient (DDPG) Reinforcement Learning for intelligent logistics and resource allocation.
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="bg-gray-500/20 text-gray-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 0 }}>DDPG</span>
                  <span className="bg-slate-500/20 text-slate-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 1 }}>Reinforcement Learning</span>
                  <span className="bg-zinc-500/20 text-zinc-300 px-2 py-1 rounded-md text-xs tech-tag" style={{ "--delay": 2 }}>Supply Chain</span>
                </div>
                <div className="flex gap-3">
                  <div className="flex items-center gap-2 bg-gray-600/20 text-gray-400 px-3 py-2 rounded-lg text-xs font-medium">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    Confidential
                  </div>
                </div>
              </div>

              {/* Empty space for centering */}
              <div className="hidden lg:block"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-24 relative">
        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"></div>
        <div className="relative w-full px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-6 tracking-tight">Services</h2>
            <p className="text-xl md:text-2xl text-gray-300 mb-4 font-semibold">What We Offer</p>
            <p className="text-gray-400 text-base md:text-lg font-medium max-w-3xl mx-auto leading-relaxed">From AI/ML solutions to full-stack development - comprehensive services for modern applications</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* AI Services */}
            <div className="group bg-gradient-to-br from-blue-900/30 to-blue-800/30 backdrop-blur-sm rounded-2xl border border-blue-500/30 p-8 hover:border-blue-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg shadow-blue-500/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-6 group-hover:text-blue-400 transition-colors duration-300 tracking-tight">AI/ML Services</h3>

              <div className="space-y-4">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-blue-400 mb-2">LLM Fine-tuning</h4>
                  <p className="text-gray-300 text-sm">Custom model training for specific domains and use cases</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-green-400 mb-2">Computer Vision</h4>
                  <p className="text-gray-300 text-sm">Object detection, image classification, and visual AI solutions</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-purple-400 mb-2">NLP & Text Processing</h4>
                  <p className="text-gray-300 text-sm">Sentiment analysis, text generation, and language understanding</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-pink-400 mb-2">Audio/Speech AI</h4>
                  <p className="text-gray-300 text-sm">Speech recognition, synthesis, and audio processing</p>
                </div>
              </div>
            </div>

            {/* Development Stack */}
            <div className="group bg-gradient-to-br from-purple-900/30 to-purple-800/30 backdrop-blur-sm rounded-2xl border border-purple-500/30 p-8 hover:border-purple-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/20">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg shadow-purple-500/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
              </div>
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-6 group-hover:text-purple-400 transition-colors duration-300 tracking-tight">Development Stack</h3>

              <div className="space-y-4">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-blue-400 mb-2">Frontend</h4>
                  <p className="text-gray-300 text-sm">React, JSX with Vite, Modern UI/UX</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-green-400 mb-2">Backend & API</h4>
                  <p className="text-gray-300 text-sm">FastAPI, Python, JavaScript, RESTful APIs</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-yellow-400 mb-2">Database</h4>
                  <p className="text-gray-300 text-sm">Firebase, Redis, Data management</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-pink-400 mb-2">Cloud & Deployment</h4>
                  <p className="text-gray-300 text-sm">GCP, AWS, Netlify, Render</p>
                </div>
              </div>
            </div>

            {/* Specialized Solutions */}
            <div className="group bg-gradient-to-br from-green-900/30 to-green-800/30 backdrop-blur-sm rounded-2xl border border-green-500/30 p-8 hover:border-green-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-green-500/20">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg shadow-green-500/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-6 group-hover:text-green-400 transition-colors duration-300 tracking-tight">Specialized Solutions</h3>

              <div className="space-y-4">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-blue-400 mb-2">AI Chatbots</h4>
                  <p className="text-gray-300 text-sm">Intelligent conversational AI for customer support</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-purple-400 mb-2">Data Analytics</h4>
                  <p className="text-gray-300 text-sm">ETL pipelines, data visualization, and insights</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-yellow-400 mb-2">Automation</h4>
                  <p className="text-gray-300 text-sm">Workflow automation and process optimization</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-pink-400 mb-2">Custom AI Models</h4>
                  <p className="text-gray-300 text-sm">Tailored AI solutions for specific business needs</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section >







      {/* Team Section */}
      < section id="team" className="py-24 relative overflow-hidden" >
        {/* Background overlay */}
        < div className="absolute inset-0 bg-black/30 backdrop-blur-[1px]" ></div >

        <div className="relative w-full px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-6 tracking-tight">Team</h2>
            <p className="text-xl md:text-2xl text-gray-300 mb-4 font-semibold">Our Background</p>
            <p className="text-gray-400 text-base md:text-lg font-medium max-w-3xl mx-auto leading-relaxed">Bringing together expertise from world-class institutions and leading tech companies</p>
          </div>

          {/* Smooth Continuous Moving Carousel */}
          <div className="relative overflow-hidden py-8">
            <div className="flex space-x-12 animate-scroll-smooth">
              {/* Render logos multiple times for seamless infinite loop */}
              {[...teamLogos, ...teamLogos, ...teamLogos, ...teamLogos].map((logo, index) => (
                <div
                  key={`${logo.alt}-${index}`}
                  className={`group flex-shrink-0 w-96 h-36 bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/20 flex items-center justify-center p-6 hover:border-${logo.color}-400/50 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-${logo.color}-400/10`}
                >
                  <img
                    src={logo.src}
                    alt={logo.alt}
                    className={`max-w-full max-h-full object-contain filter brightness-90 group-hover:brightness-110 transition-all duration-300 ${logo.scale ? 'scale-150' : ''}`}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section >

      {/* FAQs Section */}
      < section id="faqs" className="py-24 relative" >
        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"></div>
        <div className="relative w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-6 tracking-tight">FAQs</h2>
          </div>

          <div className="space-y-6">
            {/* FAQ 1 */}
            <div className="group bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/30 overflow-hidden hover:border-blue-500/50 transition-all duration-500 hover:shadow-xl hover:shadow-blue-500/10">
              <button
                onClick={() => toggleFAQ(0)}
                className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-all duration-300"
              >
                <h3 className="text-lg md:text-xl font-semibold text-white group-hover:text-blue-400 transition-colors duration-300 tracking-tight">
                  What all services does Qreate AI offer?
                </h3>
                <svg
                  className={`w-6 h-6 text-gray-400 transition-transform duration-300 ${openFAQ === 0 ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFAQ === 0 && (
                <div className="px-6 pt-2 pb-6 animate-fade-in">
                  <p className="text-gray-300 leading-relaxed">
                    We offer comprehensive AI/ML solutions including Computer Vision, Reinforcement Learning, ETL Pipelining, and Audio/Speech processing. Our development stack covers React frontends, FastAPI backends, and cloud deployment on GCP/AWS.
                  </p>
                </div>
              )}
            </div>

            {/* FAQ 2 */}
            <div className="group bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/30 overflow-hidden hover:border-purple-500/50 transition-all duration-500 hover:shadow-xl hover:shadow-purple-500/10">
              <button
                onClick={() => toggleFAQ(1)}
                className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-all duration-300"
              >
                <h3 className="text-xl font-semibold text-white group-hover:text-purple-400 transition-colors duration-300">
                  How long does a typical software development project take?
                </h3>
                <svg
                  className={`w-6 h-6 text-gray-400 transition-transform duration-300 ${openFAQ === 1 ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFAQ === 1 && (
                <div className="px-6 pt-2 pb-6 animate-fade-in">
                  <p className="text-gray-300 leading-relaxed">
                    Project timelines vary based on complexity and scope. Simple integrations can be completed in 2-4 weeks, while comprehensive AI applications typically take 8-16 weeks. We provide detailed timelines during our initial consultation.
                  </p>
                </div>
              )}
            </div>

            {/* FAQ 3 */}
            <div className="group bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/30 overflow-hidden hover:border-green-500/50 transition-all duration-500 hover:shadow-xl hover:shadow-green-500/10">
              <button
                onClick={() => toggleFAQ(2)}
                className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-all duration-300"
              >
                <h3 className="text-xl font-semibold text-white group-hover:text-green-400 transition-colors duration-300">
                  What does the broader process look like?
                </h3>
                <svg
                  className={`w-6 h-6 text-gray-400 transition-transform duration-300 ${openFAQ === 2 ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFAQ === 2 && (
                <div className="px-6 pt-2 pb-6 animate-fade-in">
                  <p className="text-gray-300 leading-relaxed">
                    Our process follows three key phases: Ideation (understanding your vision and goals), Development (designing and building your solution), and Launch (deploying and providing ongoing support). We maintain close collaboration throughout each phase.
                  </p>
                </div>
              )}
            </div>

            {/* FAQ 4 */}
            <div className="group bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/30 overflow-hidden hover:border-pink-500/50 transition-all duration-500 hover:shadow-xl hover:shadow-pink-500/10">
              <button
                onClick={() => toggleFAQ(3)}
                className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-all duration-300"
              >
                <h3 className="text-xl font-semibold text-white group-hover:text-pink-400 transition-colors duration-300">
                  What kind of quality assurance do we have? Do we offer maintenance?
                </h3>
                <svg
                  className={`w-6 h-6 text-gray-400 transition-transform duration-300 ${openFAQ === 3 ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFAQ === 3 && (
                <div className="px-6 pt-2 pb-6 animate-fade-in">
                  <p className="text-gray-300 leading-relaxed">
                    We implement comprehensive testing protocols and quality assurance measures throughout development. Yes, we provide ongoing maintenance and support services to ensure your AI solutions continue to perform optimally and stay updated with the latest technologies.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section >

      {/* Footer */}
      < footer className="text-white py-16 relative overflow-hidden" >
        <div className="absolute inset-0 bg-black/50 backdrop-blur-[1px]"></div>
        <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 z-10"></div>
        <div className="relative w-full px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-3xl md:text-4xl font-extrabold mb-6 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent tracking-tight">Qreate AI</h3>
            <p className="text-gray-400 mb-12 text-lg md:text-xl font-medium">AI-native product studio building fullstack AI/LLM apps</p>
            <div className="flex justify-center space-x-12 mb-8">
              <a href="https://calendly.com/qareailabs" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 relative group">
                Contact Us
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#about" className="text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 relative group">
                About
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
            </div>

            {/* Copyright */}
            <div className="border-t border-gray-700/50 pt-8">
              <p className="text-gray-500 text-sm">
                All rights reserved © Qare AI Pvt. Ltd.
              </p>
            </div>
          </div>
        </div>
      </footer >

      {/* Floating Action Button */}
      < div className="fixed bottom-8 right-8 z-50" >
        <button className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-110 animate-pulse-glow rocket-boost">
          <svg className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
        </button>
      </div >
    </div >
  )
}

export default App
