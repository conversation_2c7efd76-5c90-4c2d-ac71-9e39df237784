import { useEffect, useRef } from 'react'
import * as THREE from 'three'

const Background3D = () => {
  const mountRef = useRef(null)
  const animationIdRef = useRef(null)
  const mouseRef = useRef({ x: 0, y: 0 })
  const targetMouseRef = useRef({ x: 0, y: 0 })

  useEffect(() => {
    if (!mountRef.current) return

    console.log('Background3D: Initializing...')

    // Scene setup
    const scene = new THREE.Scene()
    scene.background = new THREE.Color(0x000000)

    // Camera setup
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
    camera.position.z = 5

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      preserveDrawingBuffer: false,
      powerPreference: "high-performance"
    })
    renderer.setSize(window.innerWidth, window.innerHeight, false)
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))

    // Ensure canvas styling for proper display
    renderer.domElement.style.position = 'absolute'
    renderer.domElement.style.top = '0'
    renderer.domElement.style.left = '0'
    renderer.domElement.style.width = '100%'
    renderer.domElement.style.height = '100%'
    renderer.domElement.style.pointerEvents = 'auto'

    mountRef.current.appendChild(renderer.domElement)

    // Create space stars (distant background)
    const createStars = () => {
      const starGeometry = new THREE.BufferGeometry()
      const starCount = 2000
      const starPositions = new Float32Array(starCount * 3)
      const starColors = new Float32Array(starCount * 3)
      const starSizes = new Float32Array(starCount)

      for (let i = 0; i < starCount * 3; i += 3) {
        // Distribute stars in a large sphere
        const radius = 50 + Math.random() * 100
        const theta = Math.random() * Math.PI * 2
        const phi = Math.acos(2 * Math.random() - 1)

        starPositions[i] = radius * Math.sin(phi) * Math.cos(theta)
        starPositions[i + 1] = radius * Math.sin(phi) * Math.sin(theta)
        starPositions[i + 2] = radius * Math.cos(phi)

        // Star colors - white, blue-white, yellow-white
        const starType = Math.random()
        if (starType < 0.6) {
          // White stars
          starColors[i] = 1.0; starColors[i + 1] = 1.0; starColors[i + 2] = 1.0
        } else if (starType < 0.8) {
          // Blue-white stars
          starColors[i] = 0.8; starColors[i + 1] = 0.9; starColors[i + 2] = 1.0
        } else {
          // Yellow-white stars
          starColors[i] = 1.0; starColors[i + 1] = 0.9; starColors[i + 2] = 0.7
        }

        starSizes[i / 3] = Math.random() * 0.02 + 0.005
      }

      starGeometry.setAttribute('position', new THREE.BufferAttribute(starPositions, 3))
      starGeometry.setAttribute('color', new THREE.BufferAttribute(starColors, 3))
      starGeometry.setAttribute('size', new THREE.BufferAttribute(starSizes, 1))

      const starMaterial = new THREE.PointsMaterial({
        size: 0.01,
        vertexColors: true,
        transparent: true,
        opacity: 0.9,
        blending: THREE.AdditiveBlending,
        sizeAttenuation: true
      })

      return new THREE.Points(starGeometry, starMaterial)
    }

    // Create floating cosmic dust/nebula particles
    const createCosmicDust = () => {
      const dustGeometry = new THREE.BufferGeometry()
      const dustCount = 800
      const dustPositions = new Float32Array(dustCount * 3)
      const dustColors = new Float32Array(dustCount * 3)
      const dustVelocities = new Float32Array(dustCount * 3)

      for (let i = 0; i < dustCount * 3; i += 3) {
        dustPositions[i] = (Math.random() - 0.5) * 30
        dustPositions[i + 1] = (Math.random() - 0.5) * 30
        dustPositions[i + 2] = (Math.random() - 0.5) * 30

        // Slower cosmic drift
        dustVelocities[i] = (Math.random() - 0.5) * 0.005
        dustVelocities[i + 1] = (Math.random() - 0.5) * 0.005
        dustVelocities[i + 2] = (Math.random() - 0.5) * 0.005

        // Nebula colors - purple, blue, pink cosmic dust
        const dustType = Math.random()
        if (dustType < 0.3) {
          // Purple nebula
          dustColors[i] = 0.6; dustColors[i + 1] = 0.2; dustColors[i + 2] = 0.8
        } else if (dustType < 0.6) {
          // Blue nebula
          dustColors[i] = 0.2; dustColors[i + 1] = 0.4; dustColors[i + 2] = 0.9
        } else {
          // Pink/magenta nebula
          dustColors[i] = 0.8; dustColors[i + 1] = 0.3; dustColors[i + 2] = 0.6
        }
      }

      dustGeometry.setAttribute('position', new THREE.BufferAttribute(dustPositions, 3))
      dustGeometry.setAttribute('color', new THREE.BufferAttribute(dustColors, 3))

      const dustMaterial = new THREE.PointsMaterial({
        size: 0.04,
        vertexColors: true,
        transparent: true,
        opacity: 0.6,
        blending: THREE.AdditiveBlending
      })

      const dust = new THREE.Points(dustGeometry, dustMaterial)
      dust.userData = { velocities: dustVelocities }
      return dust
    }

    const stars = createStars()
    const cosmicDust = createCosmicDust()
    scene.add(stars)
    scene.add(cosmicDust)

    // Create a glowing sun
    const createSun = () => {
      const sunGeometry = new THREE.SphereGeometry(1.2, 32, 32)
      const sunMaterial = new THREE.MeshBasicMaterial({
        color: 0xffaa00,
        transparent: true,
        opacity: 0.8
      })
      const sun = new THREE.Mesh(sunGeometry, sunMaterial)
      sun.position.set(-20, 15, -30) // Far away

      // Create sun glow effect
      const glowGeometry = new THREE.SphereGeometry(1.8, 32, 32)
      const glowMaterial = new THREE.MeshBasicMaterial({
        color: 0xff6600,
        transparent: true,
        opacity: 0.3,
        blending: THREE.AdditiveBlending
      })
      const sunGlow = new THREE.Mesh(glowGeometry, glowMaterial)
      sunGlow.position.copy(sun.position)

      // Create outer glow
      const outerGlowGeometry = new THREE.SphereGeometry(2.5, 32, 32)
      const outerGlowMaterial = new THREE.MeshBasicMaterial({
        color: 0xffdd44,
        transparent: true,
        opacity: 0.1,
        blending: THREE.AdditiveBlending
      })
      const outerGlow = new THREE.Mesh(outerGlowGeometry, outerGlowMaterial)
      outerGlow.position.copy(sun.position)

      scene.add(sun)
      scene.add(sunGlow)
      scene.add(outerGlow)

      return { sun, sunGlow, outerGlow }
    }

    // Create distant galaxies/nebulae
    const createGalaxies = () => {
      const galaxies = []
      for (let i = 0; i < 3; i++) {
        const galaxyGeometry = new THREE.RingGeometry(3, 8, 32)
        const galaxyMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color().setHSL(0.7 + i * 0.1, 0.6, 0.3),
          transparent: true,
          opacity: 0.15,
          side: THREE.DoubleSide,
          blending: THREE.AdditiveBlending
        })
        const galaxy = new THREE.Mesh(galaxyGeometry, galaxyMaterial)
        galaxy.position.set(
          (Math.random() - 0.5) * 80,
          (Math.random() - 0.5) * 80,
          -40 - Math.random() * 20
        )
        galaxy.rotation.x = Math.random() * Math.PI
        galaxy.rotation.y = Math.random() * Math.PI
        galaxy.userData = { rotationSpeed: Math.random() * 0.001 + 0.0005 }
        galaxies.push(galaxy)
        scene.add(galaxy)
      }
      return galaxies
    }

    // Create space stations/satellites
    const createSpaceStations = () => {
      const stations = []
      for (let i = 0; i < 2; i++) {
        // Main body
        const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.3, 1.5, 8)
        const bodyMaterial = new THREE.MeshBasicMaterial({
          color: 0x666666,
          wireframe: true,
          transparent: true,
          opacity: 0.6
        })
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial)

        // Solar panels
        const panelGeometry = new THREE.BoxGeometry(2, 0.1, 0.8)
        const panelMaterial = new THREE.MeshBasicMaterial({
          color: 0x0066cc,
          wireframe: true,
          transparent: true,
          opacity: 0.4
        })
        const panel1 = new THREE.Mesh(panelGeometry, panelMaterial)
        const panel2 = new THREE.Mesh(panelGeometry, panelMaterial)
        panel1.position.set(1.2, 0, 0)
        panel2.position.set(-1.2, 0, 0)

        // Group them together
        const station = new THREE.Group()
        station.add(body)
        station.add(panel1)
        station.add(panel2)

        station.position.set(
          (Math.random() - 0.5) * 25,
          (Math.random() - 0.5) * 25,
          (Math.random() - 0.5) * 20
        )

        station.userData = {
          rotationSpeed: { x: Math.random() * 0.002, y: Math.random() * 0.002, z: Math.random() * 0.002 },
          orbitSpeed: Math.random() * 0.0005 + 0.0002
        }

        stations.push(station)
        scene.add(station)
      }
      return stations
    }

    // Create comets
    const createComets = () => {
      const comets = []
      for (let i = 0; i < 2; i++) {
        // Comet head
        const headGeometry = new THREE.SphereGeometry(0.15, 16, 16)
        const headMaterial = new THREE.MeshBasicMaterial({
          color: 0xaaaaaa,
          transparent: true,
          opacity: 0.8
        })
        const head = new THREE.Mesh(headGeometry, headMaterial)

        // Comet tail (using particles)
        const tailGeometry = new THREE.BufferGeometry()
        const tailCount = 50
        const tailPositions = new Float32Array(tailCount * 3)
        const tailColors = new Float32Array(tailCount * 3)

        for (let j = 0; j < tailCount; j++) {
          const index = j * 3
          tailPositions[index] = -j * 0.1 // Trail behind
          tailPositions[index + 1] = (Math.random() - 0.5) * 0.2
          tailPositions[index + 2] = (Math.random() - 0.5) * 0.2

          const intensity = 1 - (j / tailCount)
          tailColors[index] = 0.8 * intensity
          tailColors[index + 1] = 0.9 * intensity
          tailColors[index + 2] = 1.0 * intensity
        }

        tailGeometry.setAttribute('position', new THREE.BufferAttribute(tailPositions, 3))
        tailGeometry.setAttribute('color', new THREE.BufferAttribute(tailColors, 3))

        const tailMaterial = new THREE.PointsMaterial({
          size: 0.02,
          vertexColors: true,
          transparent: true,
          opacity: 0.6,
          blending: THREE.AdditiveBlending
        })

        const tail = new THREE.Points(tailGeometry, tailMaterial)

        const comet = new THREE.Group()
        comet.add(head)
        comet.add(tail)

        comet.position.set(
          (Math.random() - 0.5) * 30,
          (Math.random() - 0.5) * 30,
          (Math.random() - 0.5) * 30
        )

        comet.userData = {
          velocity: new THREE.Vector3(
            (Math.random() - 0.5) * 0.02,
            (Math.random() - 0.5) * 0.02,
            (Math.random() - 0.5) * 0.02
          )
        }

        comets.push(comet)
        scene.add(comet)
      }
      return comets
    }

    const sunSystem = createSun()
    const galaxies = createGalaxies()
    const spaceStations = createSpaceStations()
    const comets = createComets()

    // Create space objects (planets, asteroids, space stations)
    const spaceObjects = []

    // Planets (spheres with cosmic colors)
    for (let i = 0; i < 4; i++) {
      const geometry = new THREE.SphereGeometry(0.4 + Math.random() * 0.4, 20, 20)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.6 + i * 0.15, 0.7, 0.5),
        wireframe: true,
        transparent: true,
        opacity: 0.3
      })
      const planet = new THREE.Mesh(geometry, material)
      planet.position.set(
        (Math.random() - 0.5) * 15,
        (Math.random() - 0.5) * 15,
        (Math.random() - 0.5) * 15
      )
      planet.userData = {
        type: 'planet',
        rotationSpeed: { x: Math.random() * 0.003, y: Math.random() * 0.003, z: Math.random() * 0.003 },
        orbitSpeed: Math.random() * 0.001 + 0.0005
      }
      spaceObjects.push(planet)
      scene.add(planet)
    }

    // Space rings/portals (torus)
    for (let i = 0; i < 3; i++) {
      const geometry = new THREE.TorusGeometry(0.8 + Math.random() * 0.6, 0.05 + Math.random() * 0.05, 8, 24)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.8 + i * 0.1, 0.9, 0.6),
        wireframe: true,
        transparent: true,
        opacity: 0.4
      })
      const ring = new THREE.Mesh(geometry, material)
      ring.position.set(
        (Math.random() - 0.5) * 12,
        (Math.random() - 0.5) * 12,
        (Math.random() - 0.5) * 12
      )
      ring.userData = {
        type: 'ring',
        rotationSpeed: { x: Math.random() * 0.004, y: Math.random() * 0.004, z: Math.random() * 0.004 },
        orbitSpeed: Math.random() * 0.0008 + 0.0003
      }
      spaceObjects.push(ring)
      scene.add(ring)
    }

    // Asteroids (irregular shapes)
    for (let i = 0; i < 3; i++) {
      const geometry = new THREE.OctahedronGeometry(0.2 + Math.random() * 0.3)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.1 + i * 0.05, 0.6, 0.4),
        wireframe: true,
        transparent: true,
        opacity: 0.5
      })
      const asteroid = new THREE.Mesh(geometry, material)
      asteroid.position.set(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
      )
      asteroid.userData = {
        type: 'asteroid',
        rotationSpeed: { x: Math.random() * 0.006, y: Math.random() * 0.006, z: Math.random() * 0.006 },
        orbitSpeed: Math.random() * 0.0012 + 0.0008
      }
      spaceObjects.push(asteroid)
      scene.add(asteroid)
    }

    // Mouse interaction with cursor direction tracking
    let lastMouseX = 0
    let lastMouseY = 0
    let mouseVelocityX = 0
    let mouseVelocityY = 0

    const updateCursorDirection = (deltaX, deltaY) => {
      if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
        // Calculate angle in degrees (0 degrees = pointing right, 90 = down)
        const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)

        // Create rotated cursor SVG
        const rotatedCursor = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='%23ffffff' stroke='%2300ffff' stroke-width='1' transform='rotate(${angle} 16 16)'%3E%3Cpath d='M16 2 L20 8 L18 10 L16 8 L14 10 L12 8 Z'/%3E%3Cpath d='M16 8 L18 12 L16 14 L14 12 Z'/%3E%3Cpath d='M12 8 L8 12 L10 14 L14 12 Z'/%3E%3Cpath d='M20 8 L24 12 L22 14 L18 12 Z'/%3E%3Cpath d='M14 12 L18 12 L20 16 L16 18 L12 16 Z'/%3E%3Cpath d='M10 14 L8 18 L12 16 Z'/%3E%3Cpath d='M22 14 L24 18 L20 16 Z'/%3E%3Cpath d='M12 16 L16 18 L20 16 L18 20 L16 22 L14 20 Z'/%3E%3Ccircle cx='8' cy='8' r='1' fill='%2300ffff' opacity='0.8'%3E%3Canimate attributeName='opacity' values='0.8;0.3;0.8' dur='2s' repeatCount='indefinite'/%3E%3C/circle%3E%3Ccircle cx='24' cy='8' r='1' fill='%2300ffff' opacity='0.8'%3E%3Canimate attributeName='opacity' values='0.3;0.8;0.3' dur='2s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E`

        // Update cursor style
        if (mountRef.current) {
          mountRef.current.style.cursor = `url("${rotatedCursor}") 16 16, auto`
        }
      }
    }

    const handleMouseMove = (event) => {
      targetMouseRef.current.x = (event.clientX / window.innerWidth) * 2 - 1
      targetMouseRef.current.y = -(event.clientY / window.innerHeight) * 2 + 1

      // Calculate mouse velocity for cursor direction
      const currentMouseX = event.clientX
      const currentMouseY = event.clientY

      mouseVelocityX = currentMouseX - lastMouseX
      mouseVelocityY = currentMouseY - lastMouseY

      // Update cursor direction based on movement
      updateCursorDirection(mouseVelocityX, mouseVelocityY)

      lastMouseX = currentMouseX
      lastMouseY = currentMouseY
    }

    const handleClick = (event) => {
      // Create cosmic energy burst effect
      const burstGeometry = new THREE.RingGeometry(0, 0.1, 24)
      const burstMaterial = new THREE.MeshBasicMaterial({
        color: 0x9966ff, // Purple cosmic energy
        transparent: true,
        opacity: 0.9,
        side: THREE.DoubleSide
      })
      const burst = new THREE.Mesh(burstGeometry, burstMaterial)

      // Create secondary ring for layered effect
      const secondaryGeometry = new THREE.RingGeometry(0, 0.05, 16)
      const secondaryMaterial = new THREE.MeshBasicMaterial({
        color: 0x66ccff, // Blue energy
        transparent: true,
        opacity: 0.7,
        side: THREE.DoubleSide
      })
      const secondaryBurst = new THREE.Mesh(secondaryGeometry, secondaryMaterial)

      const mouse = new THREE.Vector2(
        (event.clientX / window.innerWidth) * 2 - 1,
        -(event.clientY / window.innerHeight) * 2 + 1
      )
      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, camera)
      const intersectPoint = raycaster.ray.at(5, new THREE.Vector3())

      burst.position.copy(intersectPoint)
      burst.lookAt(camera.position)
      secondaryBurst.position.copy(intersectPoint)
      secondaryBurst.lookAt(camera.position)

      scene.add(burst)
      scene.add(secondaryBurst)

      let burstScale = 0
      const animateBurst = () => {
        burstScale += 0.05 // Slower expansion
        burst.scale.setScalar(burstScale)
        secondaryBurst.scale.setScalar(burstScale * 1.5)

        burst.material.opacity = Math.max(0, 0.9 - burstScale * 0.08)
        secondaryBurst.material.opacity = Math.max(0, 0.7 - burstScale * 0.1)

        // Add rotation for cosmic effect
        burst.rotation.z += 0.02
        secondaryBurst.rotation.z -= 0.03

        if (burst.material.opacity > 0) {
          requestAnimationFrame(animateBurst)
        } else {
          scene.remove(burst)
          scene.remove(secondaryBurst)
          burst.geometry.dispose()
          burst.material.dispose()
          secondaryBurst.geometry.dispose()
          secondaryBurst.material.dispose()
        }
      }
      animateBurst()
    }

    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('click', handleClick)

    // Animation loop with better error handling
    const animate = () => {
      if (!mountRef.current || !renderer || !scene || !camera) return

      animationIdRef.current = requestAnimationFrame(animate)

      // Smooth mouse following (slower)
      mouseRef.current.x += (targetMouseRef.current.x - mouseRef.current.x) * 0.02
      mouseRef.current.y += (targetMouseRef.current.y - mouseRef.current.y) * 0.02

      const time = Date.now() * 0.0001 // Much slower time scale

      // Animate cosmic dust
      const dustPositions = cosmicDust.geometry.attributes.position.array
      const dustVelocities = cosmicDust.userData.velocities
      for (let i = 0; i < dustPositions.length; i += 3) {
        dustPositions[i] += dustVelocities[i] + mouseRef.current.x * 0.0005
        dustPositions[i + 1] += dustVelocities[i + 1] + mouseRef.current.y * 0.0005
        dustPositions[i + 2] += dustVelocities[i + 2]

        // Gentle boundary wrapping for cosmic effect
        if (Math.abs(dustPositions[i]) > 15) dustPositions[i] *= -0.9
        if (Math.abs(dustPositions[i + 1]) > 15) dustPositions[i + 1] *= -0.9
        if (Math.abs(dustPositions[i + 2]) > 15) dustPositions[i + 2] *= -0.9
      }
      cosmicDust.geometry.attributes.position.needsUpdate = true

      // Slow rotation for stars (distant background)
      stars.rotation.x += 0.0002
      stars.rotation.y += 0.0001

      // Gentle cosmic dust rotation
      cosmicDust.rotation.x += 0.0003
      cosmicDust.rotation.y += 0.0005

      // Animate space objects with orbital motion
      spaceObjects.forEach((object, index) => {
        const userData = object.userData

        // Individual rotation
        object.rotation.x += userData.rotationSpeed.x
        object.rotation.y += userData.rotationSpeed.y
        object.rotation.z += userData.rotationSpeed.z

        // Orbital motion around center
        const orbitRadius = 8 + index * 2
        const orbitAngle = time * userData.orbitSpeed + index * Math.PI * 0.5

        object.position.x = Math.cos(orbitAngle) * orbitRadius + mouseRef.current.x * 0.5
        object.position.y = Math.sin(orbitAngle) * orbitRadius * 0.7 + mouseRef.current.y * 0.5
        object.position.z = Math.sin(orbitAngle * 0.5) * 3
      })

      // Animate sun system
      sunSystem.sun.rotation.y += 0.001
      sunSystem.sunGlow.rotation.x += 0.0008
      sunSystem.sunGlow.rotation.y += 0.0012
      sunSystem.outerGlow.rotation.z += 0.0005

      // Pulsing sun effect
      const sunPulse = 1 + Math.sin(time * 5) * 0.1
      sunSystem.sunGlow.scale.setScalar(sunPulse)
      sunSystem.outerGlow.scale.setScalar(sunPulse * 0.8)

      // Animate galaxies
      galaxies.forEach((galaxy, index) => {
        galaxy.rotation.z += galaxy.userData.rotationSpeed
        // Gentle floating motion
        galaxy.position.y += Math.sin(time * 2 + index * Math.PI) * 0.01
      })

      // Animate space stations
      spaceStations.forEach((station, index) => {
        station.rotation.x += station.userData.rotationSpeed.x
        station.rotation.y += station.userData.rotationSpeed.y
        station.rotation.z += station.userData.rotationSpeed.z

        // Orbital motion
        const stationOrbitRadius = 12 + index * 3
        const stationOrbitAngle = time * station.userData.orbitSpeed + index * Math.PI
        station.position.x = Math.cos(stationOrbitAngle) * stationOrbitRadius
        station.position.y = Math.sin(stationOrbitAngle) * stationOrbitRadius * 0.5
      })

      // Animate comets
      comets.forEach((comet, index) => {
        comet.position.add(comet.userData.velocity)

        // Reset comet position if it goes too far
        if (comet.position.length() > 50) {
          comet.position.set(
            (Math.random() - 0.5) * 30,
            (Math.random() - 0.5) * 30,
            (Math.random() - 0.5) * 30
          )
        }

        // Rotate comet slightly
        comet.rotation.x += 0.002
        comet.rotation.y += 0.001
      })

      // Gentle camera drift through space
      camera.position.x = Math.sin(time * 0.3) * 0.3 + mouseRef.current.x * 0.2
      camera.position.y = Math.cos(time * 0.2) * 0.2 + mouseRef.current.y * 0.2
      camera.position.z = 5 + Math.sin(time * 0.1) * 0.5
      camera.lookAt(mouseRef.current.x * 0.5, mouseRef.current.y * 0.5, 0)

      renderer.render(scene, camera)
    }

    animate()

    // Enhanced resize and display change handling with debouncing
    let resizeTimeout
    const handleResize = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        const width = window.innerWidth
        const height = window.innerHeight
        const pixelRatio = Math.min(window.devicePixelRatio, 2)

        camera.aspect = width / height
        camera.updateProjectionMatrix()

        renderer.setSize(width, height, false)
        renderer.setPixelRatio(pixelRatio)

        // Force a render to prevent glitches
        renderer.render(scene, camera)

        console.log('Background3D: Resized to', width, 'x', height, 'with pixel ratio', pixelRatio)
      }, 100)
    }

    // Handle display changes (moving between monitors)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible again, force resize
        setTimeout(handleResize, 100)
      }
    }

    // Handle focus changes
    const handleFocus = () => {
      setTimeout(handleResize, 100)
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    console.log('Background3D: Setup complete')

    // Cleanup
    return () => {
      console.log('Background3D: Cleaning up...')
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('click', handleClick)

      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement)
      }
      renderer.dispose()

      scene.traverse((object) => {
        if (object.geometry) object.geometry.dispose()
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose())
          } else {
            object.material.dispose()
          }
        }
      })
    }
  }, [])

  return (
    <div
      ref={mountRef}
      className="fixed inset-0 z-0 space-cursor"
      style={{
        background: 'linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%)'
      }}
    />
  )
}

export default Background3D
